-- 膳食营养分析平台数据库初始化脚本
-- 创建数据库
CREATE DATABASE IF NOT EXISTS `meals` 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE `meals`;

-- 创建用户表
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `email` varchar(100) NOT NULL COMMENT '邮箱地址',
  `password_hash` varchar(255) NOT NULL COMMENT '密码哈希值',
  `status` tinyint NOT NULL DEFAULT '0' COMMENT '用户状态：0-正常，1-禁用，2-锁定',
  `email_verified` tinyint NOT NULL DEFAULT '0' COMMENT '邮箱验证状态：0-未验证，1-已验证',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  `last_login_ip` varchar(45) DEFAULT NULL COMMENT '最后登录IP',
  `failed_login_attempts` int NOT NULL DEFAULT '0' COMMENT '登录失败次数',
  `locked_until` datetime DEFAULT NULL COMMENT '账户锁定到期时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除标记：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_users_email` (`email`),
  UNIQUE KEY `uk_users_username` (`username`),
  KEY `idx_users_status` (`status`),
  KEY `idx_users_email_verified` (`email_verified`),
  KEY `idx_users_created_at` (`created_at`),
  KEY `idx_users_deleted` (`deleted`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 创建用户会话表（可选，用于记录登录会话）
DROP TABLE IF EXISTS `user_sessions`;
CREATE TABLE `user_sessions` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '会话ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `session_token` varchar(255) NOT NULL COMMENT '会话令牌',
  `refresh_token` varchar(255) DEFAULT NULL COMMENT '刷新令牌',
  `device_info` varchar(500) DEFAULT NULL COMMENT '设备信息',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(1000) DEFAULT NULL COMMENT '用户代理',
  `expires_at` datetime NOT NULL COMMENT '过期时间',
  `created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint NOT NULL DEFAULT '0' COMMENT '逻辑删除标记：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_sessions_token` (`session_token`),
  KEY `idx_sessions_user_id` (`user_id`),
  KEY `idx_sessions_expires_at` (`expires_at`),
  KEY `idx_sessions_deleted` (`deleted`),
  CONSTRAINT `fk_sessions_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户会话表';

-- 创建登录日志表
DROP TABLE IF EXISTS `login_logs`;
CREATE TABLE `login_logs` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` bigint DEFAULT NULL COMMENT '用户ID',
  `email` varchar(100) DEFAULT NULL COMMENT '登录邮箱',
  `ip_address` varchar(45) NOT NULL COMMENT 'IP地址',
  `user_agent` varchar(1000) DEFAULT NULL COMMENT '用户代理',
  `login_result` tinyint NOT NULL COMMENT '登录结果：0-失败，1-成功',
  `failure_reason` varchar(255) DEFAULT NULL COMMENT '失败原因',
  `login_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
  PRIMARY KEY (`id`),
  KEY `idx_login_logs_user_id` (`user_id`),
  KEY `idx_login_logs_email` (`email`),
  KEY `idx_login_logs_ip` (`ip_address`),
  KEY `idx_login_logs_result` (`login_result`),
  KEY `idx_login_logs_time` (`login_time`),
  CONSTRAINT `fk_login_logs_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='登录日志表';

-- 插入测试数据（可选）
-- 密码为 "password123" 的BCrypt哈希值
INSERT INTO `users` (`username`, `email`, `password_hash`, `status`, `email_verified`) VALUES
('admin', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewfBPd92.ChOdg1u', 0, 1),
('testuser', '<EMAIL>', '$2a$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewfBPd92.ChOdg1u', 0, 0);

-- 创建索引优化查询性能
-- 复合索引用于登录验证
CREATE INDEX `idx_users_email_status` ON `users` (`email`, `status`, `deleted`);
CREATE INDEX `idx_users_username_status` ON `users` (`username`, `status`, `deleted`);

-- 复合索引用于会话管理
CREATE INDEX `idx_sessions_user_expires` ON `user_sessions` (`user_id`, `expires_at`, `deleted`);

-- 复合索引用于日志查询
CREATE INDEX `idx_login_logs_user_time` ON `login_logs` (`user_id`, `login_time`);
CREATE INDEX `idx_login_logs_ip_time` ON `login_logs` (`ip_address`, `login_time`);

-- 创建视图用于用户统计（可选）
CREATE OR REPLACE VIEW `user_stats` AS
SELECT 
    COUNT(*) AS total_users,
    COUNT(CASE WHEN status = 0 THEN 1 END) AS active_users,
    COUNT(CASE WHEN status = 1 THEN 1 END) AS disabled_users,
    COUNT(CASE WHEN status = 2 THEN 1 END) AS locked_users,
    COUNT(CASE WHEN email_verified = 1 THEN 1 END) AS verified_users,
    COUNT(CASE WHEN email_verified = 0 THEN 1 END) AS unverified_users,
    COUNT(CASE WHEN DATE(created_at) = CURDATE() THEN 1 END) AS today_registrations,
    COUNT(CASE WHEN DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 END) AS week_registrations,
    COUNT(CASE WHEN DATE(created_at) >= DATE_SUB(CURDATE(), INTERVAL 30 DAY) THEN 1 END) AS month_registrations
FROM users 
WHERE deleted = 0;

-- 创建存储过程用于清理过期会话（可选）
DELIMITER //
CREATE PROCEDURE CleanExpiredSessions()
BEGIN
    DELETE FROM user_sessions 
    WHERE expires_at < NOW() 
    OR deleted = 1;
    
    SELECT ROW_COUNT() AS cleaned_sessions;
END //
DELIMITER ;

-- 创建存储过程用于用户统计报告（可选）
DELIMITER //
CREATE PROCEDURE GetUserReport(IN days INT)
BEGIN
    SELECT 
        DATE(created_at) AS registration_date,
        COUNT(*) AS new_users,
        COUNT(CASE WHEN email_verified = 1 THEN 1 END) AS verified_users
    FROM users 
    WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL days DAY)
    AND deleted = 0
    GROUP BY DATE(created_at)
    ORDER BY registration_date DESC;
END //
DELIMITER ;

-- 设置数据库字符集和排序规则
ALTER DATABASE `meals` CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci;

-- 显示创建结果
SELECT 'Database and tables created successfully!' AS result;
