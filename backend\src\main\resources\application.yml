# 膳食营养分析平台配置文件
server:
  port: 8080
  servlet:
    context-path: /api
    encoding:
      charset: UTF-8
      enabled: true

spring:
  application:
    name: nutrition-analysis

  # 输出配置
  output:
    ansi:
      enabled: always  # 强制启用ANSI颜色输出

  # 日志配置
  logging:
    pattern:
      console: "%clr(%d{yyyy-MM-dd HH:mm:ss}){faint} %clr([%thread]){magenta} %clr(%-5level){highlight} %clr(%logger{36}){cyan} - %msg%n"
      file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    level:
      com.meals.nutrition: DEBUG
      org.springframework.security: INFO
      org.springframework.data.redis: INFO
      org.apache.ibatis: DEBUG
  
  # 数据库配置
  datasource:
    url: ******************************************************************************************************************
    username: root
    password: "021026"
    driver-class-name: com.mysql.cj.jdbc.Driver

  # Redis 配置
  data:
    redis:
      host: localhost
      port: 6379
      password:
      database: 0
      timeout: 5000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1ms
    
  # MyBatis 配置
  mybatis:
    mapper-locations: classpath:dao/*.xml
    type-aliases-package: com.meals.nutrition.entity
    configuration:
      map-underscore-to-camel-case: true
      log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

  # MyBatis Plus 配置
  mybatis-plus:
    mapper-locations: classpath:dao/*.xml
    type-aliases-package: com.meals.nutrition.entity
    configuration:
      map-underscore-to-camel-case: true
      log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    global-config:
      db-config:
        id-type: auto
        logic-delete-field: deleted
        logic-delete-value: 1
        logic-not-delete-value: 0
    
  # Jackson 配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
    default-property-inclusion: non_null

# 日志配置
logging:
  level:
    com.meals.nutrition: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# Swagger/OpenAPI 配置
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    enabled: true

# JWT 配置
jwt:
  secret: "mySecretKey123456789012345678901234567890123456789012345678901234567890"
  expiration: 86400000  # 24小时 (毫秒)
  refresh-expiration: 604800000  # 7天 (毫秒)
  remember-me-expiration: 2592000000  # 30天 (毫秒)

# 应用安全配置
app:
  security:
    # 登录失败限制
    login-attempt:
      max-attempts: 10
      lock-duration: 300000  # 5分钟 (毫秒)
    # 密码策略
    password:
      min-length: 8
      require-uppercase: true
      require-lowercase: true
      require-digit: true
      require-special-char: false
