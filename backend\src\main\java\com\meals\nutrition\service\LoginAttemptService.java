package com.meals.nutrition.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 登录尝试限制服务
 * 用于防止暴力破解攻击
 * 
 * <AUTHOR> Team
 * @since 2024-01-01
 */
@Slf4j
@Service
public class LoginAttemptService {

    private static final String LOGIN_ATTEMPT_PREFIX = "login_attempt:";
    private static final String IP_ATTEMPT_PREFIX = "ip_attempt:";

    @Autowired(required = false)
    private RedisTemplate<String, Object> redisTemplate;

    // 内存备用存储（当Redis不可用时使用）
    private final ConcurrentHashMap<String, AttemptInfo> memoryStore = new ConcurrentHashMap<>();

    @Value("${app.security.login-attempt.max-attempts:10}")
    private int maxAttempts;

    @Value("${app.security.login-attempt.lock-duration:300000}")
    private long lockDurationMs;

    /**
     * 尝试信息内部类（用于内存存储）
     */
    private static class AttemptInfo {
        private int attempts;
        private LocalDateTime lockUntil;

        public AttemptInfo(int attempts, LocalDateTime lockUntil) {
            this.attempts = attempts;
            this.lockUntil = lockUntil;
        }

        public int getAttempts() { return attempts; }
        public void setAttempts(int attempts) { this.attempts = attempts; }
        public LocalDateTime getLockUntil() { return lockUntil; }
        public void setLockUntil(LocalDateTime lockUntil) { this.lockUntil = lockUntil; }

        public boolean isLocked() {
            return lockUntil != null && lockUntil.isAfter(LocalDateTime.now());
        }
    }

    /**
     * 记录登录失败
     *
     * @param identifier 标识符（用户名、邮箱或IP）
     * @param isUser 是否为用户标识符（true）还是IP标识符（false）
     */
    public void recordLoginFailure(String identifier, boolean isUser) {
        String key = (isUser ? LOGIN_ATTEMPT_PREFIX : IP_ATTEMPT_PREFIX) + identifier;

        try {
            if (redisTemplate != null) {
                // 使用Redis存储
                Integer attempts = (Integer) redisTemplate.opsForValue().get(key);
                if (attempts == null) {
                    attempts = 0;
                }
                attempts++;
                redisTemplate.opsForValue().set(key, attempts, lockDurationMs, TimeUnit.MILLISECONDS);
            } else {
                // 使用内存存储
                AttemptInfo info = memoryStore.get(key);
                if (info == null) {
                    info = new AttemptInfo(1, null);
                } else {
                    info.setAttempts(info.getAttempts() + 1);
                }

                // 如果达到最大尝试次数，设置锁定时间
                if (info.getAttempts() >= maxAttempts) {
                    info.setLockUntil(LocalDateTime.now().plusSeconds(lockDurationMs / 1000));
                }

                memoryStore.put(key, info);
            }

            log.warn("记录登录失败: {} ({})", identifier, isUser ? "用户" : "IP");

        } catch (Exception e) {
            log.error("记录登录失败时发生错误: {}", e.getMessage(), e);
        }
    }

    /**
     * 检查是否被锁定
     *
     * @param identifier 标识符
     * @param isUser 是否为用户标识符
     * @return 是否被锁定
     */
    public boolean isLocked(String identifier, boolean isUser) {
        String key = (isUser ? LOGIN_ATTEMPT_PREFIX : IP_ATTEMPT_PREFIX) + identifier;

        try {
            if (redisTemplate != null) {
                // 使用Redis检查
                Integer attempts = (Integer) redisTemplate.opsForValue().get(key);
                return attempts != null && attempts >= maxAttempts;
            } else {
                // 使用内存检查
                AttemptInfo info = memoryStore.get(key);
                if (info == null) {
                    return false;
                }

                // 检查是否过期
                if (info.isLocked()) {
                    return true;
                } else if (info.getLockUntil() != null && !info.isLocked()) {
                    // 锁定已过期，清除记录
                    memoryStore.remove(key);
                    return false;
                }

                return info.getAttempts() >= maxAttempts;
            }
        } catch (Exception e) {
            log.error("检查锁定状态时发生错误: {}", e.getMessage(), e);
            return false; // 发生错误时不锁定，避免影响正常用户
        }
    }

    /**
     * 获取剩余锁定时间（秒）
     * 
     * @param identifier 标识符
     * @param isUser 是否为用户标识符
     * @return 剩余锁定时间（秒），如果未锁定返回0
     */
    public long getRemainingLockTime(String identifier, boolean isUser) {
        String key = (isUser ? LOGIN_ATTEMPT_PREFIX : IP_ATTEMPT_PREFIX) + identifier;
        
        try {
            if (isLocked(identifier, isUser)) {
                Long ttl = redisTemplate.getExpire(key, TimeUnit.SECONDS);
                return ttl != null && ttl > 0 ? ttl : 0;
            }
            return 0;
        } catch (Exception e) {
            log.error("获取剩余锁定时间时发生错误: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 重置登录失败次数
     *
     * @param identifier 标识符
     * @param isUser 是否为用户标识符
     */
    public void resetLoginFailures(String identifier, boolean isUser) {
        String key = (isUser ? LOGIN_ATTEMPT_PREFIX : IP_ATTEMPT_PREFIX) + identifier;

        try {
            if (redisTemplate != null) {
                redisTemplate.delete(key);
            } else {
                memoryStore.remove(key);
            }
            log.info("重置登录失败次数: {} ({})", identifier, isUser ? "用户" : "IP");
        } catch (Exception e) {
            log.error("重置登录失败次数时发生错误: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取当前失败次数
     * 
     * @param identifier 标识符
     * @param isUser 是否为用户标识符
     * @return 失败次数
     */
    public int getFailureCount(String identifier, boolean isUser) {
        String key = (isUser ? LOGIN_ATTEMPT_PREFIX : IP_ATTEMPT_PREFIX) + identifier;
        
        try {
            Integer attempts = (Integer) redisTemplate.opsForValue().get(key);
            return attempts != null ? attempts : 0;
        } catch (Exception e) {
            log.error("获取失败次数时发生错误: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 检查用户是否被锁定
     * 
     * @param identifier 用户标识符（用户名或邮箱）
     * @return 是否被锁定
     */
    public boolean isUserLocked(String identifier) {
        return isLocked(identifier, true);
    }

    /**
     * 检查IP是否被锁定
     * 
     * @param ip IP地址
     * @return 是否被锁定
     */
    public boolean isIpLocked(String ip) {
        return isLocked(ip, false);
    }

    /**
     * 记录用户登录失败
     * 
     * @param identifier 用户标识符
     */
    public void recordUserLoginFailure(String identifier) {
        recordLoginFailure(identifier, true);
    }

    /**
     * 记录IP登录失败
     * 
     * @param ip IP地址
     */
    public void recordIpLoginFailure(String ip) {
        recordLoginFailure(ip, false);
    }

    /**
     * 重置用户登录失败次数
     * 
     * @param identifier 用户标识符
     */
    public void resetUserLoginFailures(String identifier) {
        resetLoginFailures(identifier, true);
    }

    /**
     * 重置IP登录失败次数
     * 
     * @param ip IP地址
     */
    public void resetIpLoginFailures(String ip) {
        resetLoginFailures(ip, false);
    }

    /**
     * 获取用户剩余锁定时间
     * 
     * @param identifier 用户标识符
     * @return 剩余锁定时间（秒）
     */
    public long getUserRemainingLockTime(String identifier) {
        return getRemainingLockTime(identifier, true);
    }

    /**
     * 获取IP剩余锁定时间
     * 
     * @param ip IP地址
     * @return 剩余锁定时间（秒）
     */
    public long getIpRemainingLockTime(String ip) {
        return getRemainingLockTime(ip, false);
    }
}
